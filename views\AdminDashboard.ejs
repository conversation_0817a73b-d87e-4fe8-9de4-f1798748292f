<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Decor And More</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet">
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet">
    <link rel="stylesheet" href="/css/public.css">
    <link rel="stylesheet" href="/css/darkMode.css">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
      body.dark-mode table {
        background: #232323 !important;
        color: #fff !important;
        border-color: #333 !important;
      }
      body.dark-mode th, body.dark-mode td {
        background: #232323 !important;
        color: #fff !important;
        border-color: #444 !important;
      }
      body.dark-mode th {
        background: #181818 !important;
        color: #ffd700 !important;
        border-bottom: 2px solid #ffd700 !important;
      }
      body.dark-mode .table {
        border-radius: 12px !important;
        overflow: hidden;
      }

        body {
            background: #fff;
            min-height: 100vh;
            padding: 20px 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffd700;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .search-bar {
            margin-bottom: 30px;
        }

        .search-bar input {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            font-size: 16px;
        }

        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 15px;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #ddd;
        }

        td img {
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            width: 60px;
            height: 40px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        td img:hover {
            transform: scale(1.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            border: 2px solid #ffd700;
            padding: 12px 25px;
            border-radius: 30px;
            color: #ffd700;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn-primary:hover {
            background: #ffd700;
            color: #000000;
            border-color: #ffd700;
        }

        .approve-btn, .reject-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 20px;
            color: #222;
            font-weight: 500;
            margin: 0 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
        }

        .approve-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .reject-btn {
            background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%);
            color: #222 !important;
            border: 2px solid #bfa14a;
            font-weight: bold;
        }

        .approve-btn:hover, .reject-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(218, 165, 32, 0.2);
            opacity: 0.95;
            background: linear-gradient(135deg, #ffe066 0%, #d4af37 100%);
            color: #000;
        }

        .logout-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%);
            color: #222 !important;
            border: 2px solid #bfa14a;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(218, 165, 32, 0.2);
            background: linear-gradient(135deg, #ffe066 0%, #d4af37 100%);
            color: #000;
        }

        .dark-mode-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #343a40;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .dark-mode-btn:hover {
            transform: rotate(45deg);
        }

        .rating i {
            color: #ffc107;
        }

        #imageModal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            max-width: 90%;
            max-height: 90vh;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .close {
            position: absolute;
            right: 25px;
            top: 15px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            transition: 0.3s;
        }

        .close:hover {
            color: #bbb;
        }

        @media (max-width: 768px) {
            .table-container {
                padding: 15px;
            }

            th, td {
                padding: 10px;
            }

            .approve-btn, .reject-btn {
                padding: 6px 15px;
                min-width: 100px;
                margin: 2px;
            }

            td img {
                width: 50px;
                height: 35px;
            }
        }

        .btn-danger {
            background: linear-gradient(135deg, #ffd700 0%, #d4af37 100%) !important;
            color: #222 !important;
            border: 2px solid #bfa14a !important;
            font-weight: bold !important;
            transition: all 0.3s ease !important;
        }
        .btn-danger:hover, .btn-danger:focus {
            background: linear-gradient(135deg, #ffe066 0%, #d4af37 100%) !important;
            color: #000 !important;
            box-shadow: 0 3px 10px rgba(218, 165, 32, 0.2) !important;
        }

        /* Dark mode fix for white backgrounds and borders */
        body.dark-mode .card,
        body.dark-mode .table-container,
        body.dark-mode .dashboard-header,
        body.dark-mode .card-container .card {
            background: #000 !important;
            color: #fff !important;
            border-color: #222 !important;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.05) !important;
        }
        body.dark-mode .table-container {
            background: #000 !important;
            color: #fff !important;
            border-color: #222 !important;
        }
        body.dark-mode .table {
            background: #000 !important;
            color: #fff !important;
            border-color: #ffd700 !important;
        }
        body.dark-mode th, body.dark-mode td {
            background: #181818 !important;
            color: #ffd700 !important;
            border-color: #ffd700 !important;
        }
        body.dark-mode .btn-danger, body.dark-mode .logout-btn {
            background: linear-gradient(135deg, #ffd700 0%, #bfa14a 100%) !important;
            color: #181818 !important;
            border: 2px solid #ffd700 !important;
        }
        body.dark-mode .btn-danger:hover, body.dark-mode .logout-btn:hover {
            background: linear-gradient(135deg, #ffe066 0%, #ffd700 100%) !important;
            color: #000 !important;
        }
    </style>

  </head>

  <body>
    <button class="logout-btn" onclick="logout()">
      <i class="fas fa-sign-out-alt me-2"></i>Logout
    </button>

    <button onclick="toggleDarkMode()" class="dark-mode-btn">
      <i class="fas fa-moon"></i>
    </button>

    <div class="container">
      <div class="dashboard-header">
        <h1 class="mb-0 section-gradient-title">Admin Dashboard</h1>
        <div class="d-flex align-items-center">
          <span class="text-white me-3">Welcome, Admin</span>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="row mb-4">
        <div class="col-md-3 mb-3">
          <div class="card stats-card"
            style="background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); color: #ffd700; border: 2px solid #ffd700;">
            <div class="card-body text-center">
              <i class="fas fa-users fa-2x mb-3"></i>
              <h5 class="card-title">Total Engineers</h5>
              <h3 class="mb-0"><%= engineers.length %></h3>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="card stats-card"
            style="background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); color: #ffd700; border: 2px solid #ffd700;">
            <div class="card-body text-center">
              <i class="fas fa-user-clock fa-2x mb-3"></i>
              <h5 class="card-title">Pending Engineers</h5>
              <h3 class="mb-0" id="pendingCount">0</h3>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="card stats-card"
            style="background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); color: #ffd700; border: 2px solid #ffd700;">
            <div class="card-body text-center">
              <i class="fas fa-users fa-2x mb-3"></i>
              <h5 class="card-title">Total Clients</h5>
              <h3 class="mb-0" id="clientCount">
                <% /* This will be populated from the server side with actual
                client count */ %>
                <i class="fas fa-spinner fa-spin" id="clientCountLoader"></i>
              </h3>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="card stats-card"
            style="background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); color: #ffd700; border: 2px solid #ffd700;">
            <div class="card-body text-center">
              <i class="fas fa-star fa-2x mb-3"></i>
              <h5 class="card-title">Avg. Rating</h5>
              <h3 class="mb-0">
                <% let totalRating = 0, ratedEngineers = 0; %>
                <% engineers.forEach(engineer => { %>
                <% if (engineer.averageRating !== undefined &&
                engineer.averageRating !== null) { totalRating +=
                engineer.averageRating; ratedEngineers++; } %>
                <% }); %>
                <%= ratedEngineers > 0 ? (totalRating /
                ratedEngineers).toFixed(1) : 'N/A' %>
              </h3>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="card stats-card"
            style="background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); color: #ffd700; border: 2px solid #ffd700;">
            <div class="card-body text-center">
              <i class="fas fa-coins fa-2x mb-3"></i>
              <h5 class="card-title">Total Revenue</h5>
              <h3 class="mb-0"><%= totalRevenue %> EGP</h3>
            </div>
          </div>
        </div>

      </div>

      <div class="table-container mt-4">
        <h4>Commission from Bookings</h4>
        <table class="table">
          <thead>
            <tr>
              <th>Engineer</th>
              <th>eventType</th>
              <th>Package</th>
              <th>Total Price</th>
              <th>Deposit</th>
              <th>Commission</th>
              <th>Remaining</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            <% bookings.forEach(booking => { %>
            <tr>
              <td><%= booking.engineerName %></td>
              <td><%= booking.projectType %></td>
              <td><%= booking.packageName %></td>
              <td><%= booking.totalPrice || booking.price %> EGP</td>
              <td><%= booking.deposit %> EGP</td>
              <td><%= booking.commission %> EGP</td>
              <td><%= booking.remaining || (booking.totalPrice -
                booking.deposit) %> EGP</td>
              <td><%= new Date(booking.bookingDate).toLocaleDateString('en-US')
                %></td>
            </tr>
            <% }) %>
          </tbody>
        </table>
      </div>

      <div class="search-bar position-relative">
        <input type="text" id="searchInput"
          placeholder="Search for engineers by name, email, or phone..."
          onkeyup="filterEngineers()"
          style="background: #333; color: #fff; border: 1px solid #555; padding: 15px 20px 15px 45px; border-radius: 30px; width: 100%;">
        <i class="fas fa-search"
          style="position: absolute; left: 20px; top: 50%; transform: translateY(-50%); color: #aaa;"></i>
      </div>

      <div class="card-container" id="engineerCards">
        <% engineers.forEach(engineer => { %>
        <div class="card"
          data-name="<%= engineer.firstName %> <%= engineer.lastName %>"
          data-clients="<%= (engineer.clients ? engineer.clients.length : 0) + (engineer.bookings ? engineer.bookings.length : 0) %>">
          <div class="text-center">
            <img
              src="<%= engineer.profilePhoto || '/images/default-avatar.png' %>"
              alt="<%= engineer.firstName %>">
            <h3 class="mt-3"><%= engineer.firstName %> <%= engineer.lastName
              %></h3>
          </div>
          <div class="mt-3">
            <p><i class="fas fa-envelope me-2"></i><%= engineer.email %></p>
            <p><i class="fas fa-phone me-2"></i><%= engineer.phone %></p>
          </div>
          <div class="rating mb-3 text-center">
            <% let rating = Math.round(engineer.averageRating); %>
            <% for (let i = 0; i < 5; i++) { %>
            <% if (i < rating) { %>
            <i class="fas fa-star"></i>
            <% } else { %>
            <i class="far fa-star"></i>
            <% } %>
            <% } %>
            <span class="ms-2"><%= engineer.rating %></span>
          </div>
          <button class="btn btn-danger w-100"
            onclick="deleteEngineer('<%= engineer._id %>')">
            <i class="fas fa-trash-alt me-2"></i>Delete
          </button>
        </div>
        <% }) %>
      </div>

      <div class="d-flex justify-content-between align-items-center mb-3 mt-4">
        <h3 class="mb-0">Pending Engineers</h3>
        <button onclick="loadPendingEngineers()" class="btn"
          style="background-color: #ffd700; color: #111;">
          <i class="fas fa-sync-alt me-2"></i>Refresh List
        </button>
      </div>

      <div class="table-container">
        <div class="table-responsive">
          <table id="engineersTable" class="table">
            <thead class="table-dark">
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>ID Card Photo</th>
                <th>Subscription</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <!-- Will be filled dynamically -->
              <tr id="noEngineersRow">
                <td colspan="6" class="text-center py-4">
                  <i class="fas fa-user-clock fa-2x mb-3 text-muted"></i>
                  <p class="mb-0">No pending engineers found. Click
                    "Refresh List" to check again.</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="/js/darkMode.js"></script>
    <script src="../user/AdminDashboard.js"></script>
    <script>
    async function loadPendingEngineers() {
        try {
            // Show loading indicator
            document.getElementById('pendingCount').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            const response = await fetch("/pending-engineers");
            const engineers = await response.json();

            // Update the pending count in the stats card
            document.getElementById('pendingCount').textContent = engineers.length;

            // Calculate total clients (this is just an example, adjust based on your data structure)
            let totalClients = 0;
            const allEngineers = document.querySelectorAll('.card[data-name]');
            allEngineers.forEach(engineerCard => {
                // Try to find client count from data attributes if available
                const clientCount = parseInt(engineerCard.getAttribute('data-clients') || '0', 10);
                totalClients += clientCount;
            });

            // Update client count if the element exists
            const clientCountElement = document.getElementById('clientCount');
            if (clientCountElement && clientCountElement.textContent.trim() === '0') {
                clientCountElement.textContent = totalClients;
            }

            const tableBody = document.querySelector("#engineersTable tbody");
            tableBody.innerHTML = ""; // Clear previous content

            if (engineers.length === 0) {
                // Show "no engineers" message
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-user-clock fa-2x mb-3 text-muted"></i>
                            <p class="mb-0">No pending engineers found. Check back later.</p>
                        </td>
                    </tr>
                `;
                return;
            }

            engineers.forEach(engineer => {
                const row = document.createElement("tr");

                // Format subscription status
                const subscriptionStatus = engineer.hasPaidSubscription ?
                    '<span class="badge bg-success">Paid</span>' :
                    '<span class="badge bg-warning text-dark">Not Paid</span>';

                row.innerHTML = `
                    <td>${engineer.firstName} ${engineer.lastName}</td>
                    <td>${engineer.email}</td>
                    <td>${engineer.phone || 'N/A'}</td>
                    <td>
                        <img src="${engineer.idCardPhoto || '/images/default-avatar.png'}"
                             alt="${engineer.firstName}"
                             width="60" height="40"
                             class="img-thumbnail cursor-pointer"
                             onclick="openImageModal('${engineer.idCardPhoto}', '${engineer.firstName} ${engineer.lastName}')"
                             style="cursor: pointer;">
                    </td>
                    <td>${subscriptionStatus}</td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-sm" style="background-color: #28a745; color: white;" onclick="approveEngineer('${engineer.email}', this)">
                                <i class="fas fa-check me-1"></i> Approve
                            </button>
                            <button class="btn btn-sm" style="background-color: #dc3545; color: white;" onclick="rejectEngineer('${engineer.email}', this)">
                                <i class="fas fa-times me-1"></i> Reject
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);
            });
        } catch (error) {
            console.error('Error loading pending engineers:', error);
            document.getElementById('pendingCount').textContent = '0';

            // Show error message in table
            const tableBody = document.querySelector("#engineersTable tbody");
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-danger">
                        <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
                        <p class="mb-0">Error loading engineers. Please try again.</p>
                    </td>
                </tr>
            `;
        }
    }

    // Function to approve an engineer
    async function approveEngineer(email, button) {
        // Show confirmation dialog
        const result = await Swal.fire({
            title: 'Approve Engineer',
            text: 'Are you sure you want to approve this engineer? They will be able to access their account.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Approve',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d'
        });

        if (!result.isConfirmed) return;

        // Show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        try {
            const response = await fetch("/approve-engineer", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (response.ok && data.message) {
                // Remove the row with animation
                const row = button.closest('tr');
                row.style.transition = 'all 0.5s';
                row.style.backgroundColor = '#d4edda';
                row.style.opacity = '0';

                setTimeout(() => {
                    row.remove();

                    // Update pending count
                    const pendingCount = document.getElementById('pendingCount');
                    const currentCount = parseInt(pendingCount.textContent);
                    pendingCount.textContent = Math.max(0, currentCount - 1);

                    // Show success message
                    Swal.fire({
                        icon: "success",
                        title: "Engineer Approved",
                        text: "The engineer has been approved successfully and will receive an email notification.",
                        timer: 2000,
                        showConfirmButton: false,
                        position: 'top-end',
                        toast: true
                    });

                    // Check if table is now empty
                    const tableBody = document.querySelector("#engineersTable tbody");
                    if (tableBody.children.length === 0) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <i class="fas fa-check-circle fa-2x mb-3 text-success"></i>
                                    <p class="mb-0">All engineers have been processed. Great job!</p>
                                </td>
                            </tr>
                        `;
                    }
                }, 500);
            } else {
                throw new Error(data.message || 'Failed to approve engineer');
            }
        } catch (error) {
            console.error('Error:', error);

            // Reset button state
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-check me-1"></i> Approve';

            // Show error message
            Swal.fire({
                icon: "error",
                title: "Error",
                text: error.message || "Failed to approve engineer",
                timer: 3000,
                showConfirmButton: false,
                position: 'top-end',
                toast: true
            });
        }
    }

    // Function to reject an engineer
    async function rejectEngineer(email, button) {
        // Show confirmation dialog
        const result = await Swal.fire({
            title: 'Reject Engineer',
            text: 'Are you sure you want to reject this engineer? This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, Reject',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d'
        });

        if (!result.isConfirmed) return;

        // Show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        try {
            const response = await fetch("/reject-engineer", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (response.ok && data.message) {
                // Remove the row with animation
                const row = button.closest('tr');
                row.style.transition = 'all 0.5s';
                row.style.backgroundColor = '#f8d7da';
                row.style.opacity = '0';

                setTimeout(() => {
                    row.remove();

                    // Update pending count
                    const pendingCount = document.getElementById('pendingCount');
                    const currentCount = parseInt(pendingCount.textContent);
                    pendingCount.textContent = Math.max(0, currentCount - 1);

                    // Show success message
                    Swal.fire({
                        icon: "success",
                        title: "Engineer Rejected",
                        text: "The engineer has been rejected successfully.",
                        timer: 2000,
                        showConfirmButton: false,
                        position: 'top-end',
                        toast: true
                    });

                    // Check if table is now empty
                    const tableBody = document.querySelector("#engineersTable tbody");
                    if (tableBody.children.length === 0) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <i class="fas fa-check-circle fa-2x mb-3 text-success"></i>
                                    <p class="mb-0">All engineers have been processed. Great job!</p>
                                </td>
                            </tr>
                        `;
                    }
                }, 500);
            } else {
                throw new Error(data.message || 'Failed to reject engineer');
            }
        } catch (error) {
            console.error('Error:', error);

            // Reset button state
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-times me-1"></i> Reject';

            // Show error message
            Swal.fire({
                icon: "error",
                title: "Error",
                text: error.message || "Failed to reject engineer",
                timer: 3000,
                showConfirmButton: false,
                position: 'top-end',
                toast: true
            });
        }
    }

    function openImageModal(imageSrc, engineerName = '') {
        const modal = document.getElementById("imageModal");
        const fullImage = document.getElementById("fullImage");
        const caption = document.getElementById("imageCaption");

        // Show loading spinner
        fullImage.src = '';
        fullImage.style.display = 'none';
        modal.style.display = "flex";

        // Create and show loading spinner
        const spinner = document.createElement('div');
        spinner.id = 'imageLoadingSpinner';
        spinner.style.cssText = 'width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #ffd700; border-radius: 50%; animation: spin 1s linear infinite; margin: 50px auto;';
        modal.querySelector('div').insertBefore(spinner, fullImage);

        // Add animation style if not already present
        if (!document.getElementById('spinnerAnimation')) {
            const style = document.createElement('style');
            style.id = 'spinnerAnimation';
            style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
            document.head.appendChild(style);
        }

        // Set caption
        caption.textContent = engineerName ? `ID Card Photo - ${engineerName}` : 'ID Card Photo';

        // Load image
        const img = new Image();
        img.onload = function() {
            // Remove spinner
            const spinner = document.getElementById('imageLoadingSpinner');
            if (spinner) spinner.remove();

            // Show image
            fullImage.src = imageSrc;
            fullImage.style.display = 'block';
        };

        img.onerror = function() {
            // Remove spinner
            const spinner = document.getElementById('imageLoadingSpinner');
            if (spinner) spinner.remove();

            // Show error message
            fullImage.style.display = 'none';
            caption.innerHTML = '<span style="color: #ff6b6b;">Error loading image</span><br><small>The image may be unavailable or the URL is invalid.</small>';
        };

        img.src = imageSrc || '/images/default-avatar.png';

        // Close modal when clicking outside the image
        modal.onclick = function(event) {
            if (event.target === modal) {
                closeImageModal();
            }
        };
    }

    function closeImageModal() {
        const modal = document.getElementById("imageModal");

        // Add fade-out animation
        modal.style.opacity = '0';
        modal.style.transition = 'opacity 0.3s ease';

        // Remove modal after animation completes
        setTimeout(() => {
            modal.style.display = "none";
            modal.style.opacity = '1';

            // Remove spinner if exists
            const spinner = document.getElementById('imageLoadingSpinner');
            if (spinner) spinner.remove();
        }, 300);
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `custom-alert ${type}`;

        const icon = type === 'success' ? '✓' : type === 'warning' ? '⚠️' : '✕';
        const title = type === 'success' ? 'Success!' : type === 'warning' ? 'Warning!' : 'Error!';

        alertDiv.innerHTML = `
            <div class="alert-icon">${icon}</div>
            <div class="alert-content">
                <div class="alert-title">${title}</div>
                <div class="alert-message">${message}</div>
            </div>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            alertDiv.style.animation = 'slideOut 0.3s ease-out forwards';
            setTimeout(() => {
                document.body.removeChild(alertDiv);
            }, 300);
        }, 3000);
    }
    </script>

    <!-- Image Viewer Modal -->
    <div id="imageModal" class="modal"
      style="display: none; position: fixed; z-index: 1050; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.9); backdrop-filter: blur(5px); align-items: center; justify-content: center;">
      <div style="position: relative; max-width: 90%; max-height: 90%;">
        <span class="close" onclick="closeImageModal()"
          style="position: absolute; top: -40px; right: 0; color: #fff; font-size: 35px; font-weight: bold; cursor: pointer;">&times;</span>
        <img class="modal-content" id="fullImage"
          style="display: block; max-width: 100%; max-height: 80vh; margin: auto; border: 2px solid #fff; border-radius: 5px; box-shadow: 0 0 20px rgba(0,0,0,0.5);">
        <div
          style="text-align: center; padding: 10px; color: white; margin-top: 15px;">
          <p id="imageCaption" style="margin: 0; font-size: 16px;">ID Card
            Photo</p>
          <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.8;">Click
            outside the image to close</p>
        </div>
      </div>
    </div>

    <!-- Load pending engineers on page load -->
    <script>
      // Load pending engineers and client count when the page loads
      document.addEventListener('DOMContentLoaded', function() {
        loadPendingEngineers();
        fetchClientCount();
      });

      // Function to fetch client count from server
      async function fetchClientCount() {
        try {
          const response = await fetch('/admin/client-count');
          const data = await response.json();

          if (response.ok) {
            // Update the client count in the stats card
            const clientCountElement = document.getElementById('clientCount');
            if (clientCountElement) {
              // Remove the loading spinner
              clientCountElement.innerHTML = data.count;
            }
          } else {
            throw new Error(data.message || 'Failed to fetch client count');
          }
        } catch (error) {
          console.error('Error fetching client count:', error);
          // Show error in the client count element
          const clientCountElement = document.getElementById('clientCount');
          if (clientCountElement) {
            clientCountElement.innerHTML = '<span title="Error loading client count">--</span>';
          }
        }
      }
    </script>

  </body>

</html>
