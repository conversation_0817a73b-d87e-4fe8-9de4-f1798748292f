/* ===== MOBILE RESPONSIVE STYLES ===== */

/* Base Mobile Styles */
@media (max-width: 992px) {
  /* Typography */
  body {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  h1 {
    font-size: 2rem !important;
  }
  h2 {
    font-size: 1.75rem !important;
  }
  h3 {
    font-size: 1.5rem !important;
  }
  h4 {
    font-size: 1.25rem !important;
  }

  /* Container and Layout */
  .container {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  /* Navigation Fixes */
  .navv-bar {
    position: relative !important;
  }

  .menu-btn {
    display: block !important;
    font-size: 28px !important;
    cursor: pointer !important;
    color: #fd226a !important;
    z-index: 1001 !important;
    padding: 10px !important;
    background: none !important;
    border: none !important;
  }

  .nav-links {
    display: none !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    background: rgba(255, 255, 255, 0.98) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    backdrop-filter: blur(15px) !important;
    z-index: 1000 !important;
    padding: 80px 20px 20px 20px !important;
    overflow-y: auto !important;
  }

  .nav-links.active {
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: center !important;
  }

  .nav-links ul {
    flex-direction: column !important;
    gap: 25px !important;
    width: 100% !important;
    max-width: 300px !important;
    margin: 0 auto !important;
  }

  .nav-links li {
    width: 100% !important;
    text-align: center !important;
  }

  .nav-links li a {
    font-size: 20px !important;
    padding: 15px 20px !important;
    display: block !important;
    text-align: center !important;
    border-radius: 10px !important;
    background: rgba(253, 34, 106, 0.1) !important;
    color: #333 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
  }

  .nav-links li a:hover {
    background: #fd226a !important;
    color: white !important;
    transform: translateY(-2px) !important;
  }

  /* Dropdown fixes for mobile */
  .dropdown {
    position: relative !important;
    width: 100% !important;
  }

  .dropdown > a {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
  }

  .dropdown-menu {
    position: static !important;
    display: none !important;
    background: rgba(253, 34, 106, 0.05) !important;
    border-radius: 8px !important;
    margin-top: 10px !important;
    padding: 10px !important;
    width: 100% !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  .dropdown:hover .dropdown-menu,
  .dropdown.active .dropdown-menu {
    display: block !important;
    animation: slideDown 0.3s ease !important;
  }

  .dropdown-menu a {
    font-size: 16px !important;
    padding: 12px 15px !important;
    margin: 5px 0 !important;
    background: rgba(253, 34, 106, 0.1) !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    transition: all 0.3s ease !important;
  }

  .dropdown-menu a:hover {
    background: rgba(253, 34, 106, 0.2) !important;
    transform: translateX(5px) !important;
  }

  /* Special styling for logout button */
  .dropdown-menu a[onclick*="logout"] {
    background: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
  }

  .dropdown-menu a[onclick*="logout"]:hover {
    background: #dc3545 !important;
    color: white !important;
  }

  /* Animation for dropdown */
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Close button for mobile menu */
  .nav-links::before {
    content: "✕" !important;
    position: absolute !important;
    top: 20px !important;
    right: 25px !important;
    font-size: 30px !important;
    color: #fd226a !important;
    cursor: pointer !important;
    z-index: 1002 !important;
  }

  /* Logo adjustments */
  .logo {
    font-size: 35px !important;
  }

  .logo-img {
    max-height: 60px !important;
  }

  /* Hero Section */
  .home-img {
    height: 70vh !important;
    background-size: cover !important;
    background-position: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .home-img h1 {
    font-size: 2.5rem !important;
    padding: 2rem 1rem !important;
    text-align: center !important;
    line-height: 1.2 !important;
    margin: 0 !important;
  }

  /* Buttons */
  .btn {
    font-size: 16px !important;
    padding: 12px 20px !important;
    border-radius: 10px !important;
    min-height: 48px !important;
    margin-bottom: 10px !important;
    display: inline-block !important;
    text-align: center !important;
  }

  .btn-primary {
    background: #fd226a !important;
    border-color: #fd226a !important;
  }

  /* Dark mode toggle button */
  .dark-mode {
    width: 48px !important;
    height: 48px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 20px !important;
  }

  /* Cards and Content */
  .card {
    margin-bottom: 20px !important;
    border-radius: 15px !important;
  }

  .card-body {
    padding: 1.5rem !important;
  }

  /* Forms */
  .form-control,
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  textarea,
  select {
    font-size: 16px !important;
    padding: 15px !important;
    border-radius: 10px !important;
    border: 2px solid #ddd !important;
    width: 100% !important;
    margin-bottom: 15px !important;
    min-height: 48px !important;
    -webkit-appearance: none !important;
    appearance: none !important;
  }

  .form-control:focus,
  input:focus,
  textarea:focus,
  select:focus {
    border-color: #fd226a !important;
    box-shadow: 0 0 0 0.2rem rgba(253, 34, 106, 0.25) !important;
    outline: none !important;
  }

  .form-group {
    margin-bottom: 20px !important;
  }

  .form-label {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    display: block !important;
  }

  /* Tables */
  .table-responsive {
    border-radius: 10px !important;
    overflow-x: auto !important;
    margin-bottom: 20px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  .table {
    font-size: 14px !important;
    margin-bottom: 0 !important;
  }

  .table th,
  .table td {
    padding: 12px 8px !important;
    white-space: nowrap !important;
    vertical-align: middle !important;
  }

  .table th {
    background-color: #f8f9fa !important;
    font-weight: 600 !important;
    border-bottom: 2px solid #dee2e6 !important;
  }

  /* Make tables more mobile-friendly */
  .table-mobile-stack {
    display: block !important;
  }

  .table-mobile-stack thead {
    display: none !important;
  }

  .table-mobile-stack tbody,
  .table-mobile-stack tr,
  .table-mobile-stack td {
    display: block !important;
    width: 100% !important;
  }

  .table-mobile-stack tr {
    border: 1px solid #ddd !important;
    border-radius: 10px !important;
    margin-bottom: 15px !important;
    padding: 15px !important;
    background: white !important;
  }

  .table-mobile-stack td {
    border: none !important;
    padding: 8px 0 !important;
    text-align: left !important;
  }

  .table-mobile-stack td:before {
    content: attr(data-label) ": " !important;
    font-weight: bold !important;
    color: #fd226a !important;
  }

  /* Images */
  img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 10px !important;
  }

  /* Profile Images */
  .profile-img,
  .engineer-img {
    width: 80px !important;
    height: 80px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
  }

  /* Modals */
  .modal-dialog {
    margin: 10px !important;
    max-width: calc(100% - 20px) !important;
  }

  .modal-content {
    border-radius: 15px !important;
  }

  /* Gallery */
  .gallery-item {
    margin-bottom: 15px !important;
  }

  .gallery-item img {
    height: 200px !important;
    border-radius: 10px !important;
  }

  /* Engineer Cards */
  .engineer-card {
    margin-bottom: 20px !important;
    padding: 15px !important;
    border-radius: 15px !important;
  }

  /* Package Cards */
  .package-card {
    margin-bottom: 20px !important;
    padding: 15px !important;
  }

  /* Booking Forms */
  .booking-form {
    padding: 20px !important;
    border-radius: 15px !important;
  }

  /* Footer */
  footer {
    padding: 30px 15px !important;
  }

  footer .row > div {
    margin-bottom: 30px !important;
  }

  /* Profile and Dashboard specific */
  .profile-section,
  .dashboard-section {
    padding: 20px 15px !important;
    margin-bottom: 20px !important;
  }

  .profile-header {
    text-align: center !important;
    margin-bottom: 30px !important;
  }

  .profile-stats {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 15px !important;
    justify-content: center !important;
  }

  .stat-item {
    flex: 1 1 calc(50% - 7.5px) !important;
    min-width: 120px !important;
    text-align: center !important;
    padding: 15px !important;
    background: rgba(253, 34, 106, 0.1) !important;
    border-radius: 10px !important;
  }

  .booking-card,
  .package-card,
  .project-card {
    margin-bottom: 20px !important;
    padding: 20px !important;
    border-radius: 15px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  /* Admin Dashboard */
  .admin-stats {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
    gap: 15px !important;
    margin-bottom: 30px !important;
  }

  .admin-stat-card {
    padding: 20px !important;
    text-align: center !important;
    border-radius: 10px !important;
    background: linear-gradient(135deg, #fd226a, #ff6b9d) !important;
    color: white !important;
  }

  /* Utility Classes */
  .text-center-mobile {
    text-align: center !important;
  }

  .mb-mobile {
    margin-bottom: 20px !important;
  }

  .p-mobile {
    padding: 15px !important;
  }

  .full-width-mobile {
    width: 100% !important;
  }

  .flex-column-mobile {
    flex-direction: column !important;
  }

  /* Hide on Mobile */
  .hide-mobile {
    display: none !important;
  }

  /* Show only on Mobile */
  .show-mobile {
    display: block !important;
  }

  /* Spacing utilities */
  .mt-mobile-20 {
    margin-top: 20px !important;
  }
  .mb-mobile-20 {
    margin-bottom: 20px !important;
  }
  .p-mobile-15 {
    padding: 15px !important;
  }
  .px-mobile-10 {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}

/* Extra Small Devices */
@media (max-width: 576px) {
  .home-img h1 {
    font-size: 2.5rem !important;
  }

  .container {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .card-body {
    padding: 1rem !important;
  }

  .btn {
    font-size: 14px !important;
    padding: 10px 15px !important;
  }

  .table {
    font-size: 12px !important;
  }

  .modal-dialog {
    margin: 5px !important;
    max-width: calc(100% - 10px) !important;
  }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .home-img {
    height: 80vh !important;
  }

  .home-img h1 {
    font-size: 2.5rem !important;
  }
}

/* Touch Improvements */
@media (hover: none) and (pointer: coarse) {
  .btn,
  .nav-links a,
  .card {
    transition: none !important;
  }

  .btn:active {
    transform: scale(0.98) !important;
  }

  /* Larger touch targets */
  .nav-links a,
  .btn,
  .form-control {
    min-height: 44px !important;
  }
}

/* Dark Mode Mobile Adjustments */
@media (max-width: 992px) {
  body.dark-mode .nav-links {
    background: rgba(0, 0, 0, 0.98) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    backdrop-filter: blur(15px) !important;
  }

  body.dark-mode .nav-links li a {
    background: rgba(253, 34, 106, 0.2) !important;
    color: #fff !important;
  }

  body.dark-mode .nav-links li a:hover {
    background: #fd226a !important;
    color: white !important;
  }

  body.dark-mode .dropdown-menu {
    background: rgba(253, 34, 106, 0.1) !important;
  }

  body.dark-mode .dropdown-menu a {
    background: rgba(253, 34, 106, 0.2) !important;
    color: #fff !important;
  }

  body.dark-mode .card {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
  }

  body.dark-mode .form-control,
  body.dark-mode input,
  body.dark-mode textarea,
  body.dark-mode select {
    background: #2a2a2a !important;
    border: 2px solid #444 !important;
    color: #fff !important;
  }

  body.dark-mode .form-control:focus,
  body.dark-mode input:focus,
  body.dark-mode textarea:focus,
  body.dark-mode select:focus {
    border-color: #fd226a !important;
    box-shadow: 0 0 0 0.2rem rgba(253, 34, 106, 0.25) !important;
  }

  body.dark-mode .btn-primary {
    background: #fd226a !important;
    border-color: #fd226a !important;
  }

  body.dark-mode .table {
    background: #1a1a1a !important;
    color: #fff !important;
  }

  body.dark-mode .table th {
    background-color: #2a2a2a !important;
    color: #fff !important;
    border-bottom: 2px solid #444 !important;
  }

  body.dark-mode .table-mobile-stack tr {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
  }

  body.dark-mode .table-mobile-stack td:before {
    color: #fd226a !important;
  }

  body.dark-mode .menu-btn {
    color: #fd226a !important;
  }
}

/* Fix for specific elements that might overflow */
* {
  box-sizing: border-box !important;
}

html,
body {
  overflow-x: hidden !important;
}

/* Prevent zoom on input focus (iOS) */
@media (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  textarea,
  select {
    font-size: 16px !important;
  }
}
